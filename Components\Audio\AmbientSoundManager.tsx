import React, { useEffect, useRef, useState } from 'react';

// Types pour les différents types d'ambiances sonores - Basés sur les fichiers réels
type AmbientSoundType = 
  // Nuit profonde
  | 'hibou-molkom' | 'night-atmosphere-with-crickets-374652'
  // Aube  
  | 'village_morning_birds_roosters'
  // Lever de soleil
  | 'blackbird'
  // Matin
  | 'insect_bee_fly' | 'morning-birdsong'
  // Midi
  | 'forest_cicada'
  // Après-midi
  | 'birds-singing' | 'summer-insects-243572'
  // Coucher de soleil
  | 'bird-chirp' | 'grillon-drome'
  // Crépuscule
  | 'cricket-single' | 'merle-blackbird'
  // Anciens types (pour compatibilité)
  | 'birds' | 'wind' | 'rain' | 'waves' | 'forest' | 'night'
  | 'none';         // Silence

interface AmbientSoundManagerProps {
  skyMode: string;
  enabled?: boolean;
  volume?: number; // 0 à 1
}

// 🔧 CISCO: Configuration audio EXCLUSIVE - UN SEUL MODE ACTIF À LA FOIS
const SOUND_CONFIG: Record<string, {
  sound: AmbientSoundType;
  volume: number;
  folder: string;
  isShort?: boolean; // Pour les fichiers courts qui doivent être en boucle
  fadeInDuration: number; // Durée du fondu d'entrée en ms - FIXÉ À 3000ms
  fadeOutDuration: number; // Durée du fondu de sortie en ms - FIXÉ À 3000ms
}> = {
  night: {
    sound: 'night-atmosphere-with-crickets-374652',
    volume: 0.6,
    folder: 'nuit-profonde',
    fadeInDuration: 3000, // 🔧 CISCO: 3 secondes pour toutes les transitions
    fadeOutDuration: 3000
  },
  dusk: {
    sound: 'merle-blackbird',
    volume: 0.4,
    folder: 'crepuscule',
    isShort: true,
    fadeInDuration: 3000, // 🔧 CISCO: 3 secondes pour toutes les transitions
    fadeOutDuration: 3000
  },
  dawn: {
    sound: 'village_morning_birds_roosters',
    volume: 0.5,
    folder: 'aube',
    fadeInDuration: 3000, // 🔧 CISCO: 3 secondes pour toutes les transitions
    fadeOutDuration: 3000
  },
  sunrise: {
    sound: 'blackbird',
    volume: 0.6,
    folder: 'lever-soleil',
    isShort: true,
    fadeInDuration: 3000, // 🔧 CISCO: 3 secondes pour toutes les transitions
    fadeOutDuration: 3000
  },
  morning: {
    sound: 'morning-birdsong',
    volume: 0.7,
    folder: 'matin',
    fadeInDuration: 3000, // 🔧 CISCO: 3 secondes pour toutes les transitions
    fadeOutDuration: 3000
  },
  midday: {
    sound: 'forest_cicada',
    volume: 0.3,
    folder: 'midi',
    isShort: true,
    fadeInDuration: 3000, // 🔧 CISCO: 3 secondes pour toutes les transitions
    fadeOutDuration: 3000
  },
  afternoon: {
    sound: 'summer-insects-243572',
    volume: 0.4,
    folder: 'apres-midi',
    fadeInDuration: 3000, // 🔧 CISCO: 3 secondes pour toutes les transitions
    fadeOutDuration: 3000
  },
  sunset: {
    sound: 'grillon-drome',
    volume: 0.4,
    folder: 'coucher-soleil',
    isShort: true,
    fadeInDuration: 3000, // 🔧 CISCO: 3 secondes pour toutes les transitions
    fadeOutDuration: 3000
  }
};

// 🔧 CISCO: Système de normalisation audio pour équilibrer les volumes
const AUDIO_NORMALIZATION: Record<AmbientSoundType, number> = {
  // Nuit profonde
  'night-atmosphere-with-crickets-374652': 1.0,  // Volume de référence
  'hibou-molkom': 0.8,                            // Hibou plus doux

  // Aube
  'village_morning_birds_roosters': 0.9,          // Coqs un peu plus doux

  // Lever de soleil
  'blackbird': 1.1,                               // Merle un peu plus fort

  // Matin
  'morning-birdsong': 1.0,                        // Chants d'oiseaux référence
  'insect_bee_fly': 0.7,                          // Bourdonnement plus doux

  // Midi
  'forest_cicada': 1.2,                           // Cigales plus fortes (naturel)

  // Après-midi
  'summer-insects-243572': 0.9,                   // Insectes d'été modérés
  'birds-singing': 1.0,                           // Chants d'oiseaux référence

  // Coucher de soleil
  'grillon-drome': 0.8,                           // Grillons doux
  'bird-chirp': 1.1,                              // Pépiements plus audibles

  // Crépuscule
  'merle-blackbird': 1.0,                         // Merle référence
  'cricket-single': 0.6,                          // Grillon unique très doux

  // Anciens types (compatibilité)
  'birds': 1.0, 'wind': 1.0, 'rain': 1.0, 'waves': 1.0, 'forest': 1.0, 'night': 1.0, 'none': 0.0
};

// Fonction pour obtenir le volume normalisé d'un son
const getNormalizedVolume = (soundType: AmbientSoundType, baseVolume: number): number => {
  const normalizationFactor = AUDIO_NORMALIZATION[soundType] || 1.0;
  return baseVolume * normalizationFactor;
};

// Fonction pour générer les URLs selon la structure des fichiers réels
const getSoundUrl = (soundType: AmbientSoundType, folder?: string): string => {
  if (soundType === 'none') return '';

  // Pour les anciens types (compatibilité), utiliser l'ancien système
  if (['birds', 'wind', 'rain', 'waves', 'forest', 'night'].includes(soundType)) {
    return `/sounds/${soundType}.mp3`;
  }

  // Pour les nouveaux types (noms de fichiers réels), utiliser la structure organisée
  if (folder) {
    return `/sounds/${folder}/${soundType}.mp3`;
  }

  // Fallback vers le dossier racine
  return `/sounds/${soundType}.mp3`;
};

const AmbientSoundManager: React.FC<AmbientSoundManagerProps> = ({
  skyMode,
  enabled = false, // 🔧 CISCO: Audio désactivé par défaut - activation manuelle
  volume = 0.5
}) => {
  // 🔧 CISCO: SYSTÈME AUDIO EXCLUSIF - UN SEUL MODE ACTIF
  const audioRef = useRef<HTMLAudioElement | null>(null); // Son principal UNIQUE
  const [currentSound, setCurrentSound] = useState<AmbientSoundType>('none');
  const [isPlaying, setIsPlaying] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [activeModeAudio, setActiveModeAudio] = useState<string>('none'); // 🔧 CISCO: Mode audio actuel
  // 🔧 CISCO: État pour tracker le mode actuel et éviter les changements redondants
  const [currentMode, setCurrentMode] = useState<string>(skyMode);

  // 🔧 CISCO: État pour ignorer temporairement les changements automatiques
  const [manualModeActive, setManualModeActive] = useState<boolean>(false);
  const manualModeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 🔧 CISCO: Fonction simplifiée - Un seul son par mode (pas d'alternatives)
  const selectRandomSound = (config: typeof SOUND_CONFIG[string]): AmbientSoundType => {
    return config.sound; // 🔧 CISCO: Mode exclusif - un seul son par mode
  };

  // 🔧 CISCO: FONCTION SUPPRIMÉE - Plus de mixage simultané (mode exclusif)

  // 🔧 CISCO: Fonction de changement immédiat de mode audio avec priorité absolue
  const handleImmediateModeChange = async (newMode: string) => {
    console.log(`🎵 AmbientSoundManager: Changement FORCÉ vers ${newMode} depuis ${currentMode}`);

    // 🔧 CISCO: Activer le mode manuel (ignore les changements automatiques)
    setManualModeActive(true);

    // Nettoyer l'ancien timeout
    if (manualModeTimeoutRef.current) {
      clearTimeout(manualModeTimeoutRef.current);
    }

    // Désactiver le mode manuel après 3 secondes
    manualModeTimeoutRef.current = setTimeout(() => {
      console.log('🔄 Mode manuel désactivé, retour à l\'auto-détection');
      setManualModeActive(false);
    }, 3000);

    // Éviter les changements redondants
    if (newMode === currentMode && !manualModeActive) {
      console.log('🔄 Mode identique, pas de changement audio');
      return;
    }

    setCurrentMode(newMode);

    if (!enabled) {
      console.log('🔇 Audio désactivé, pas de changement de son');
      return;
    }

    const config = SOUND_CONFIG[newMode] || { sound: 'none' as AmbientSoundType, volume: 0, folder: '' };
    console.log(`🎵 Configuration audio FORCÉE pour ${newMode}:`, config);

    // Changement immédiat sans attendre les useEffect
    await changeSoundTo(config);
  };

  // 🔧 CISCO: FONCTION EXCLUSIVE - UN SEUL SON ACTIF À LA FOIS
  const changeSoundTo = async (soundConfig: typeof SOUND_CONFIG[string]) => {
    if (!enabled) {
      await fadeOutAndStop();
      return;
    }

    console.log(`🎵 CISCO: Changement EXCLUSIF vers ${soundConfig.sound}`);

    const selectedSound = selectRandomSound(soundConfig);
    const targetVolume = soundConfig.volume;

    // 🔧 CISCO: Normalisation du volume
    const normalizedVolume = getNormalizedVolume(selectedSound, targetVolume);

    // Si c'est le même son, ajuster seulement le volume
    if (currentSound === selectedSound && audioRef.current && !isTransitioning) {
      smoothVolumeTransition(normalizedVolume * volume, 1500);
      return;
    }

    // Si on est déjà en transition, attendre qu'elle se termine
    if (isTransitioning) {
      console.log('🎵 Transition déjà en cours, attente...');
      return;
    }

    // 🔧 CISCO: Cross-fade EXCLUSIF vers le nouveau son (3 secondes)
    await crossFadeToNewSoundExclusive(selectedSound, normalizedVolume, soundConfig);
  };

  // 🔧 CISCO: FONCTION SUPPRIMÉE - Remplacée par crossFadeToNewSoundExclusive

  // 🔧 CISCO: NOUVELLE FONCTION EXCLUSIVE - Cross-fade avec transitions de 3 secondes
  const crossFadeToNewSoundExclusive = async (newSoundType: AmbientSoundType, targetVolume: number, config: typeof SOUND_CONFIG[string]) => {
    setIsTransitioning(true);

    try {
      console.log(`🎵 CISCO: Début transition EXCLUSIVE vers ${newSoundType} (3s fadeOut + 3s fadeIn)`);

      // Obtenir l'URL avec le bon dossier et la configuration
      const soundUrl = getSoundUrl(newSoundType, config.folder);

      // Étape 1: Charger le nouveau son en silence
      const newAudio = new Audio(soundUrl);
      newAudio.loop = true; // Tous les sons sont en boucle
      newAudio.volume = 0;

      // Attendre que le nouveau son soit prêt
      await new Promise<void>((resolve, reject) => {
        newAudio.addEventListener('canplaythrough', () => resolve(), { once: true });
        newAudio.addEventListener('error', reject, { once: true });
        newAudio.load();
      });

      // Étape 2: FADE OUT de l'ancien son (3 secondes) - EXCLUSIVITÉ TOTALE
      if (audioRef.current && isPlaying) {
        console.log(`🔇 CISCO: FadeOut de l'ancien son (3s)...`);
        const oldAudio = audioRef.current; // 🔧 CISCO: Sauvegarder la référence
        await performFadeOut(oldAudio, 3000); // 3 secondes exactement
        oldAudio.pause(); // 🔧 CISCO: Utiliser la référence sauvegardée
        audioRef.current = null; // 🔧 CISCO: Libération complète
      }

      // Étape 3: FADE IN du nouveau son (3 secondes)
      console.log(`🔊 CISCO: FadeIn du nouveau son ${newSoundType} (3s)...`);
      await newAudio.play();
      await performFadeIn(newAudio, targetVolume * volume, 3000); // 3 secondes exactement

      // Étape 4: Mise à jour des références
      audioRef.current = newAudio;
      setCurrentSound(newSoundType);
      setIsPlaying(true);
      setActiveModeAudio(config.folder || 'unknown'); // 🔧 CISCO: Suivi du mode actif

      console.log(`✅ CISCO: Transition EXCLUSIVE vers ${newSoundType} terminée`);
    } catch (error) {
      console.error('❌ CISCO: Erreur lors de la transition exclusive:', error);
    } finally {
      setIsTransitioning(false);
    }
  };

  // 🔧 NOUVELLE FONCTION: Transition de volume ultra fluide
  const smoothVolumeTransition = (targetVolume: number, duration: number = 2000) => {
    if (!audioRef.current) return;

    const audio = audioRef.current;
    const startVolume = audio.volume;
    const volumeDifference = targetVolume - startVolume;
    const startTime = Date.now();

    const updateVolume = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // Easing function pour une transition plus naturelle
      const easedProgress = 1 - Math.pow(1 - progress, 3); // ease-out cubic
      
      audio.volume = startVolume + (volumeDifference * easedProgress);

      if (progress < 1) {
        requestAnimationFrame(updateVolume);
      }
    };

    requestAnimationFrame(updateVolume);
  };

  // 🔧 CISCO: CROSS FADE OUT progressif synchronisé
  const performFadeOut = (audio: HTMLAudioElement, duration: number = 7500): Promise<void> => {
    return new Promise((resolve) => {
      const startTime = Date.now();
      const initialVolume = audio.volume;

      const fadeOutStep = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // CISCO: Easing doux pour cross-fade naturel (comme les nuages)
        const easedProgress = Math.pow(progress, 1.5); // ease-in plus doux

        audio.volume = initialVolume * (1 - easedProgress);

        if (progress >= 1) {
          audio.volume = 0;
          resolve();
        } else {
          requestAnimationFrame(fadeOutStep);
        }
      };

      requestAnimationFrame(fadeOutStep);
    });
  };

  // 🔧 NOUVELLE FONCTION: Cross-fade entre deux sons avec durée personnalisée
  const performCrossFade = (oldAudio: HTMLAudioElement, newAudio: HTMLAudioElement, targetVolume: number, duration: number = 3000): Promise<void> => {
    return new Promise((resolve) => {
      const startTime = Date.now();
      const initialOldVolume = oldAudio.volume;

      const crossFadeStep = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // Easing pour plus de naturel
        const easedProgress = 1 - Math.pow(1 - progress, 2); // ease-out quadratic

        // Fade out l'ancien son
        oldAudio.volume = initialOldVolume * (1 - easedProgress);
        
        // Fade in le nouveau son
        newAudio.volume = targetVolume * easedProgress;

        if (progress < 1) {
          requestAnimationFrame(crossFadeStep);
        } else {
          oldAudio.pause();
          resolve();
        }
      };

      requestAnimationFrame(crossFadeStep);
    });
  };

  // 🔧 CISCO: CROSS FADE IN progressif synchronisé
  const performFadeIn = (audio: HTMLAudioElement, targetVolume: number, duration: number = 7500): Promise<void> => {
    return new Promise((resolve) => {
      const startTime = Date.now();

      const fadeInStep = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // CISCO: Easing doux pour cross-fade naturel (comme les nuages)
        const easedProgress = Math.pow(progress, 0.7); // ease-out plus doux

        audio.volume = targetVolume * easedProgress;

        if (progress < 1) {
          requestAnimationFrame(fadeInStep);
        } else {
          resolve();
        }
      };

      requestAnimationFrame(fadeInStep);
    });
  };

  // 🔧 CISCO: Fade out et arrêt EXCLUSIF - Version simplifiée
  const fadeOutAndStop = async (): Promise<void> => {
    if (!audioRef.current || !isPlaying) return;

    const config = SOUND_CONFIG[skyMode];
    const fadeOutDuration = config?.fadeOutDuration || 3000; // 🔧 CISCO: 3 secondes par défaut

    return new Promise<void>((resolve) => {
      const audio = audioRef.current!;
      const startTime = Date.now();
      const initialVolume = audio.volume;

      const fadeOutStep = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / fadeOutDuration, 1);

        // Easing pour un fade-out plus naturel
        const easedProgress = Math.pow(progress, 2); // ease-in quadratic

        // Fade out du son principal UNIQUEMENT
        audio.volume = initialVolume * (1 - easedProgress);

        if (progress < 1) {
          requestAnimationFrame(fadeOutStep);
        } else {
          // Arrêter le son principal
          audio.pause();
          audio.volume = 0;
          audioRef.current = null; // 🔧 CISCO: Libération complète

          setIsPlaying(false);
          setCurrentSound('none');
          setActiveModeAudio('none'); // 🔧 CISCO: Reset du mode actif
          resolve();
        }
      };

      requestAnimationFrame(fadeOutStep);
    });
  };

  // Transition de volume douce
  const fadeToVolume = (targetVol: number) => {
    if (!audioRef.current) return;

    const audio = audioRef.current;
    const startVolume = audio.volume;
    const difference = targetVol - startVolume;
    const steps = 30;
    const stepAmount = difference / steps;

    let currentStep = 0;
    const fadeInterval = setInterval(() => {
      currentStep++;
      audio.volume = Math.max(0, Math.min(1, startVolume + (stepAmount * currentStep)));
      
      if (currentStep >= steps) {
        clearInterval(fadeInterval);
      }
    }, 50);
  };

  // Réagir aux changements de mode de ciel (fallback si pas de changement immédiat)
  useEffect(() => {
    // 🔧 CISCO: Ignorer les changements automatiques si mode manuel actif
    if (manualModeActive) {
      console.log(`🚫 Mode manuel actif - Ignoré changement automatique vers ${skyMode}`);
      return;
    }

    // Mettre à jour le mode local si différent
    if (skyMode !== currentMode) {
      console.log(`🎵 AmbientSoundManager: Synchronisation mode - skyMode: ${skyMode} vs currentMode: ${currentMode}`);
      setCurrentMode(skyMode);
    }

    console.log(`🎵 AmbientSoundManager: Changement automatique détecté - skyMode: ${skyMode}, enabled: ${enabled}`);
    const config = SOUND_CONFIG[skyMode] || { sound: 'none' as AmbientSoundType, volume: 0, folder: '' };
    console.log(`🎵 Configuration audio automatique pour ${skyMode}:`, config);
    changeSoundTo(config);
  }, [skyMode, enabled, currentMode, manualModeActive]);

  // Réagir aux changements de volume global
  useEffect(() => {
    if (audioRef.current && isPlaying) {
      const config = SOUND_CONFIG[skyMode] || { sound: 'none' as AmbientSoundType, volume: 0, folder: '' };
      fadeToVolume(config.volume * volume);
    }
  }, [volume]);

  // 🔧 CISCO: Écouteur d'événement global pour changement immédiat
  useEffect(() => {
    // Exposer la fonction de changement immédiat globalement
    (window as any).triggerAudioModeChange = handleImmediateModeChange;

    console.log('🎵 AmbientSoundManager: Fonction de changement immédiat exposée globalement');

    return () => {
      // Nettoyer la fonction globale
      delete (window as any).triggerAudioModeChange;
    };
  }, [handleImmediateModeChange, currentMode]);

  // 🔧 CISCO: Nettoyage lors du démontage - Version EXCLUSIVE
  useEffect(() => {
    return () => {
      // Nettoyer le son principal UNIQUEMENT
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }

      // 🔧 CISCO: Nettoyer le timer de mode manuel
      if (manualModeTimeoutRef.current) {
        clearTimeout(manualModeTimeoutRef.current);
      }
    };
  }, []);

  // Interface pour debug/contrôle - SUPPRIMÉ pour éviter les doublons
  if (!enabled) return null;

  return null; // Plus d'affichage de debug
};

export default AmbientSoundManager;
